<?php

// autoload_psr4.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    '<PERSON><PERSON>\\Traits\\' => array($baseDir . '/traits'),
    '<PERSON><PERSON>\\PaymentGateways\\' => array($baseDir . '/ecommerce/PaymentGateways'),
    '<PERSON><PERSON>\\Models\\' => array($baseDir . '/models'),
    '<PERSON><PERSON>\\Helpers\\' => array($baseDir . '/helpers'),
    '<PERSON><PERSON>\\Ecommerce\\' => array($baseDir . '/ecommerce'),
    '<PERSON><PERSON>\\Cache\\' => array($baseDir . '/cache'),
    'TUTOR\\' => array($baseDir . '/classes'),
);
